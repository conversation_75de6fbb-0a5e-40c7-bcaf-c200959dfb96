"""
Enhanced Report Formatting Engine

This module provides report-specific formatting to match example files exactly.
"""

import pandas as pd
import numpy as np
from typing import Dict, Any, List
from datetime import datetime
import calendar
from report_definitions import ReportDefinition, REPORT_REGISTRY

class ReportFormatter:
    """Enhanced formatter for generating reports that match examples exactly"""
    
    def __init__(self):
        self.registry = REPORT_REGISTRY
    
    def format_report(self, df: pd.DataFrame, report_name: str, year: int, month: int) -> pd.DataFrame:
        """
        Format a report DataFrame to match the example file exactly
        
        Args:
            df: Raw data DataFrame
            report_name: Name of the report
            year: Report year
            month: Report month
            
        Returns:
            Formatted DataFrame matching example structure
        """
        if df.empty:
            return df
        
        report_def = self.registry.get_report(report_name)
        if not report_def:
            return df
        
        # Apply column name mapping first
        formatted_df = self._apply_column_mapping(df, report_def)
        
        # Apply special processing based on report type
        for processing in report_def.special_processing:
            if processing == 'add_mtd_row':
                formatted_df = self._add_mtd_row(formatted_df)
            elif processing == 'rename_service_level_column':
                formatted_df = self._rename_service_level_column(formatted_df)
            elif processing == 'add_header_row':
                formatted_df = self._add_system_performance_header(formatted_df, year, month)
            elif processing == 'add_non_compliance_summary':
                formatted_df = self._add_non_compliance_summary(formatted_df)
        
        # Apply data type formatting
        formatted_df = self._apply_data_types(formatted_df, report_def)
        
        return formatted_df
    
    def _apply_column_mapping(self, df: pd.DataFrame, report_def: ReportDefinition) -> pd.DataFrame:
        """Apply exact column name mapping from report definition"""

        expected_columns = [col.name for col in report_def.columns]
        current_columns = list(df.columns)

        # Handle specific report mappings
        if report_def.name == 'Customer Service Performance (Multiple Channel)':
            # Filter and map columns to match example exactly
            column_mapping = {
                'case_number': 'Case Number',
                'contact_mode': 'Channel',
                'cs_actual_start_datetime': 'Actual Start DateTime',
                'cs_completed_datetime': 'Completed DateTime',
                'cs_available_duration': 'Available SLA (Second)',
                'cs_actual_duration': 'Actual SLA (Second)'
            }
            # Select only the columns we need
            available_cols = [col for col in column_mapping.keys() if col in df.columns]
            df = df[available_cols]
            df = df.rename(columns=column_mapping)

        elif report_def.name == 'Service Request':
            # Add missing EXCEED DURATION column
            if 'EXCEED DURATION' not in df.columns:
                # Calculate exceed duration
                if 'ACTUAL DURATION' in df.columns and 'EXPECTED DURATION' in df.columns:
                    actual = pd.to_numeric(df['ACTUAL DURATION'], errors='coerce').fillna(0)
                    expected = pd.to_numeric(df['EXPECTED DURATION'], errors='coerce').fillna(0)
                    df['EXCEED DURATION'] = (actual - expected).clip(lower=0).astype(int)
                else:
                    df['EXCEED DURATION'] = 0

        elif report_def.name == 'System Performance (Response Time)':
            # Don't rename columns here - let the special processing handle it
            pass

        elif report_def.name == 'Customer Service Performance (Telephone)':
            # Handle the service_level -> SLA % mapping
            if 'service_level' in df.columns:
                df = df.rename(columns={'service_level': 'SLA %'})

        else:
            # For other reports, map by position if counts match
            if len(current_columns) == len(expected_columns):
                column_mapping = dict(zip(current_columns, expected_columns))
                df = df.rename(columns=column_mapping)

        return df
    
    def _apply_data_types(self, df: pd.DataFrame, report_def: ReportDefinition) -> pd.DataFrame:
        """Apply proper data types and formatting"""
        
        for col_def in report_def.columns:
            if col_def.name in df.columns:
                col_data = df[col_def.name]
                
                if col_def.data_type == 'integer':
                    df[col_def.name] = pd.to_numeric(col_data, errors='coerce').fillna(0).astype(int)
                elif col_def.data_type == 'float':
                    df[col_def.name] = pd.to_numeric(col_data, errors='coerce').fillna(0.0)
                elif col_def.data_type == 'datetime':
                    df[col_def.name] = pd.to_datetime(col_data, errors='coerce')
                elif col_def.data_type == 'string':
                    df[col_def.name] = col_data.astype(str)
        
        return df
    
    def _add_mtd_row(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add MTD (Month-to-Date) summary row for telephone report"""
        
        if df.empty:
            return df
        
        # Convert numeric columns
        numeric_cols = ['CALL OFFER', 'CALL HANDLE', 'CALL WITHIN 10', 'ABANDON SHORT', 'ABANDON LONG']
        for col in numeric_cols:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce').fillna(0)
        
        # Calculate MTD totals
        mtd_data = {
            'DATE CALL': 'MTD',
            'CALL OFFER': int(df['CALL OFFER'].sum()),
            'CALL HANDLE': int(df['CALL HANDLE'].sum()),
            'CALL WITHIN 10': int(df['CALL WITHIN 10'].sum()),
            'ABANDON SHORT': int(df['ABANDON SHORT'].sum()),
            'ABANDON LONG': int(df['ABANDON LONG'].sum())
        }
        
        # Calculate MTD percentages
        total_calls = mtd_data['CALL OFFER']
        if total_calls > 0:
            abandon_total = mtd_data['ABANDON SHORT'] + mtd_data['ABANDON LONG']
            mtd_data['ABANDON %'] = round((abandon_total / total_calls) * 100, 6)  # Match example precision
            mtd_data['ANSWER %'] = round((mtd_data['CALL HANDLE'] / total_calls) * 100, 6)
            mtd_data['SLA %'] = round(((mtd_data['CALL WITHIN 10'] + mtd_data['ABANDON SHORT']) / total_calls) * 100, 6)
        else:
            mtd_data['ABANDON %'] = 0.0
            mtd_data['ANSWER %'] = 0.0
            mtd_data['SLA %'] = 0.0
        
        # Add MTD row
        mtd_row = pd.DataFrame([mtd_data])
        result_df = pd.concat([df, mtd_row], ignore_index=True)
        
        return result_df
    
    def _rename_service_level_column(self, df: pd.DataFrame) -> pd.DataFrame:
        """Rename service_level column to SLA %"""
        if 'service_level' in df.columns:
            df = df.rename(columns={'service_level': 'SLA %'})
        return df
    
    def _add_system_performance_header(self, df: pd.DataFrame, year: int, month: int) -> pd.DataFrame:
        """Add header row for system performance report"""

        # Create Indonesian month name for header
        indonesian_months = {
            1: 'Januari', 2: 'Februari', 3: 'Maret', 4: 'April',
            5: 'Mei', 6: 'Juni', 7: 'Juli', 8: 'Agustus',
            9: 'September', 10: 'Oktober', 11: 'November', 12: 'Desember'
        }
        month_name = indonesian_months.get(month, calendar.month_name[month])
        header_col_name = f"{month_name} {year}"

        # Define proper column names to match example exactly
        expected_columns = ['System Performance', header_col_name, 'Within', 'Total', 'Exceed %']

        if len(df.columns) >= 5:
            # Map the query result columns to expected format
            # Query returns: trans_name, within_sla, exceed_sla, total_trans, exceed_percentage, within_percentage
            # We want: trans_name, exceed_sla, within_sla, total_trans, exceed_percentage
            df_mapped = pd.DataFrame()
            df_mapped[expected_columns[0]] = df.iloc[:, 0]  # trans_name
            df_mapped[expected_columns[1]] = df.iloc[:, 2]  # exceed_sla (this will be the "Exceed" column)
            df_mapped[expected_columns[2]] = df.iloc[:, 1]  # within_sla
            df_mapped[expected_columns[3]] = df.iloc[:, 3]  # total_trans
            df_mapped[expected_columns[4]] = df.iloc[:, 4]  # exceed_percentage

            df = df_mapped

        # Create header row with proper column names
        header_data = {
            expected_columns[0]: 'Transaksi (16)',
            expected_columns[1]: 'Exceed',  # This will be under the month column
            expected_columns[2]: 'Within',
            expected_columns[3]: 'Total',
            expected_columns[4]: 'Exceed %'
        }

        header_row = pd.DataFrame([header_data])

        # Combine header and data
        result_df = pd.concat([header_row, df], ignore_index=True)

        return result_df
    
    def _add_non_compliance_summary(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add Non Compliance % summary row for system performance report"""
        
        if df.empty or len(df) < 2:  # Need at least header + 1 data row
            return df
        
        # Skip header row (index 0) and calculate totals from data rows
        data_rows = df.iloc[1:]  # Skip header row
        
        # Calculate totals (assuming columns are: name, exceed, within, total, exceed_pct)
        if len(df.columns) >= 4:
            try:
                # Convert to numeric, handling any non-numeric values
                exceed_col = pd.to_numeric(data_rows.iloc[:, 1], errors='coerce').fillna(0)
                within_col = pd.to_numeric(data_rows.iloc[:, 2], errors='coerce').fillna(0)
                total_col = pd.to_numeric(data_rows.iloc[:, 3], errors='coerce').fillna(0)
                
                total_exceed = exceed_col.sum()
                total_within = within_col.sum()
                total_trans = total_col.sum()
                
                # Calculate non-compliance percentage
                if total_trans > 0:
                    non_compliance_pct = (total_exceed / total_trans) * 100
                else:
                    non_compliance_pct = 0.0
                
                # Create summary row
                summary_data = {
                    df.columns[0]: 'Non Compliance %',
                    df.columns[1]: np.nan,  # Empty for exceed column in summary
                    df.columns[2]: np.nan,  # Empty for within column in summary  
                    df.columns[3]: np.nan,  # Empty for total column in summary
                    df.columns[4]: round(non_compliance_pct, 6)  # Match example precision
                }
                
                summary_row = pd.DataFrame([summary_data])
                result_df = pd.concat([df, summary_row], ignore_index=True)
                
                return result_df
                
            except Exception as e:
                print(f"Warning: Could not calculate non-compliance summary: {e}")
                return df
        
        return df
    
    def get_excel_formatting_options(self, report_def: ReportDefinition) -> Dict[str, Any]:
        """Get Excel formatting options for a report"""
        
        formatting = {
            'sheet_name': report_def.sheet_name,
            'index': False,
            'column_widths': {},
            'number_formats': {},
            'alignments': {}
        }
        
        # Set column widths and formats
        for col_def in report_def.columns:
            if col_def.width:
                formatting['column_widths'][col_def.name] = col_def.width
            
            if col_def.format_rule:
                formatting['number_formats'][col_def.name] = col_def.format_rule
            
            formatting['alignments'][col_def.name] = col_def.alignment
        
        return formatting


# Global formatter instance
REPORT_FORMATTER = ReportFormatter()
